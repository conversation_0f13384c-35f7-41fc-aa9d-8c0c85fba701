<template>
	<view>
		<view class="tabs-container">
			<u-tabs :list="tabsList" :current="current" @change="tabsChange" :is-scroll="false" :item-style="itemStyle"></u-tabs>
		</view>
		
		<scroll-view scroll-y style="height: calc(100vh - 44px);" @scrolltolower="loadMore" refresher-enabled
			:refresher-triggered="refreshing" @refresherrefresh="onRefresh">
			<view class="order-list">
				<template v-if="orderList.length > 0">
					<view class="order-item" v-for="(item, index) in orderList" :key="index">
						<view class="order-header">
							<text class="order-time">{{item.createTime}}</text>
							<text class="order-status">{{getStatusText(item.status)}}</text>
						</view>
						
						<view class="goods-info">
							<image :src="baseUrl + item.goodsPic.split(',')[0]" mode="aspectFill" class="goods-image"></image>
							<view class="goods-detail">
								<text class="goods-name">{{item.goodsName}}</text>
								<text class="goods-price">¥{{item.price}}</text>
								<text class="goods-num">x{{item.goodsNum}}</text>
							</view>
						</view>
						
						<view class="order-info">
							<view class="info-item">
								<text class="label">出行日期：</text>
								<text>{{item.startDate}}</text>
							</view>
							<view class="info-item">
								<text class="label">联系人：</text>
								<text>{{item.contactName}}</text>
							</view>
							<view class="info-item">
								<text class="label">联系电话：</text>
								<text>{{item.contactPhone}}</text>
							</view>
							<view class="info-item">
								<text class="label">订单金额：</text>
								<text class="price">¥{{item.price}}</text>
							</view>
						</view>
						
						<view class="order-footer">
							<view v-if="item.status === '1'" class="action-btn complete" @click="completeOrder(item)">完成</view>
							<view v-if="item.status === '0'" class="action-btn cancel" @click="cancelOrder(item)">取消</view>
						</view>
					</view>
					
					<u-loadmore :status="loadStatus" />
				</template>
				<template v-else>
					<u-empty mode="order" text="暂无订单"></u-empty>
				</template>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { listGoodsOrder, updateGoodsOrder } from "@/api/lease/goodsOrder.js"
	import { baseUrl } from "@/config"
	export default {
		data() {
			return {
				tabsList: [
					{ name: '全部' },
					{ name: '待支付' },
					{ name: '进行中' },
					{ name: '已完成' }
				],
				current: 0,
				itemStyle: {
					width: '25%',
					textAlign: 'center',
					height: '90rpx',
					lineHeight: '90rpx'
				},
				queryParams: {
					userId: this.$store.state.user.userId,
					pageNum: 1,
					pageSize: 10,
					status: ''
				},
				orderList: [],
				total: 0,
				refreshing: false,
				loadStatus: 'loadmore',
				baseUrl
			}
		},
		onLoad() {
			this.getList()
		},
		methods: {
			tabsChange(e) {
				this.current = e.index
				this.queryParams.pageNum = 1
				this.orderList = []
				switch(e.index) {
					case 0:
						this.queryParams.status = ''
						break
					case 1:
						this.queryParams.status = '0'
						break
					case 2:
						this.queryParams.status = '1'
						break
					case 3:
						this.queryParams.status = '2'
						break
				}
				this.getList()
			},
			async getList() {
				try {
					const res = await listGoodsOrder(this.queryParams)
					if (this.queryParams.pageNum === 1) {
						this.orderList = res.rows
					} else {
						this.orderList = [...this.orderList, ...res.rows]
					}
					this.total = res.total
					this.loadStatus = this.orderList.length >= this.total ? 'nomore' : 'loadmore'
				} catch (error) {
					console.error(error)
				}
			},
			loadMore() {
				if (this.orderList.length >= this.total) return
				this.queryParams.pageNum++
				this.getList()
			},
			async onRefresh() {
				this.refreshing = true
				this.queryParams.pageNum = 1
				await this.getList()
				this.refreshing = false
			},
			getStatusText(status) {
				const statusMap = {
					'0': '待支付',
					'1': '进行中',
					'2': '已完成',
					'9': '已取消'
				}
				return statusMap[status] || '未知状态'
			},
			async cancelOrder(item) {
				try {
					uni.showModal({
						title: '提示',
						content: '确认取消该订单吗？',
						success: async (res) => {
							if (res.confirm) {
								await updateGoodsOrder({
									id: item.id,
									status: '9'
								})
								uni.showToast({
									title: '取消成功',
									icon: 'success'
								})
								this.onRefresh()
							}
						}
					})
				} catch (error) {
					console.error(error)
					uni.showToast({
						title: '取消失败',
						icon: 'none'
					})
				}
			},

			completeOrder(item) {
				uni.showModal({
					title:"警告",
					content:"是否确认完成该订单？",
					success: (mRes) => {
						if(mRes.confirm){
							updateGoodsOrder({
								id: item.id,
								status: '2'
							}).then(res=>{
								this.$u.toast('操作成功')
								this.onRefresh()
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
.tabs-container {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.order-list {
	padding: 20rpx;
	
	.order-item {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		
		.order-header {
			display: flex;
			justify-content: space-between;
			padding-bottom: 20rpx;
			border-bottom: 1px solid #eee;
			
			.order-time {
				color: #666;
				font-size: 24rpx;
			}
			
			.order-status {
				color: #2979ff;
				font-size: 24rpx;
			}
		}
		
		.goods-info {
			display: flex;
			padding: 20rpx 0;
			
			.goods-image {
				width: 160rpx;
				height: 160rpx;
				border-radius: 8rpx;
			}
			
			.goods-detail {
				flex: 1;
				margin-left: 20rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				
				.goods-name {
					font-size: 28rpx;
					color: #333;
				}
				
				.goods-price {
					color: #ff5500;
					font-size: 32rpx;
				}
				
				.goods-num {
					color: #999;
					font-size: 24rpx;
				}
			}
		}
		
		.order-info {
			padding: 20rpx 0;
			border-top: 1px solid #eee;
			
			.info-item {
				display: flex;
				margin-bottom: 10rpx;
				font-size: 26rpx;
				
				.label {
					color: #666;
					width: 160rpx;
				}
				
				.price {
					color: #ff5500;
				}
			}
		}
		
		.order-footer {
			display: flex;
			justify-content: flex-end;
			padding-top: 20rpx;
			border-top: 1px solid #eee;
			
			.action-btn {
				margin-left: 20rpx;
				min-width: 120rpx;
				font-size: 24rpx;
				padding: 0 20rpx;
				height: 60rpx;
				line-height: 60rpx;
				text-align: center;
				border-radius: 30rpx;

				&.cancel {
					background-color: #ff5500;
					color: #fff;
				}

				&.complete {
					background-color: #19be6b;
					color: #fff;
				}
			}
		}
	}
}
</style>
