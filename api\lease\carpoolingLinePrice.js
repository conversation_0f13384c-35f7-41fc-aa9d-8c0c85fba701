import request from '@/utils/request'

// 查询拼车路线价格列表
export function listCarpoolingLinePrice(query) {
  return request({
    url: '/lease/carpoolingLinePrice/list',
    method: 'get',
    params: query
  })
}

// 查询拼车路线价格详细
export function getCarpoolingLinePrice(id) {
  return request({
    url: '/lease/carpoolingLinePrice/' + id,
    method: 'get'
  })
}

// 新增拼车路线价格
export function addCarpoolingLinePrice(data) {
  return request({
    url: '/lease/carpoolingLinePrice',
    method: 'post',
    data: data
  })
}

// 修改拼车路线价格
export function updateCarpoolingLinePrice(data) {
  return request({
    url: '/lease/carpoolingLinePrice',
    method: 'put',
    data: data
  })
}

// 删除拼车路线价格
export function delCarpoolingLinePrice(id) {
  return request({
    url: '/lease/carpoolingLinePrice/' + id,
    method: 'delete'
  })
}
