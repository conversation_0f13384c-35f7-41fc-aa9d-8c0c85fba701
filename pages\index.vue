<template>
	<view class="container">
		<!-- 顶部滚动通知 -->
		<u-notice-bar :text="noticeText" mode="horizontal" :speed="80"></u-notice-bar>

		<!-- 轮播图 -->
		<view class="swiper-box">
			<u-swiper :list="swiperList" height="300" :border-radius="10" :circular="true"></u-swiper>
		</view>

		<!-- 表单区域 -->
		<view class="form-container">
			<!-- 服务类型选择 -->
			<u-tabs :list="serviceTypes" :current="currentService" @change="onServiceChange"></u-tabs>

			<!-- 包车表单 -->
			<view v-if="currentService === 0">
				<u-form :model="form" ref="uForm" labelWidth="160rpx">
					<!-- 起点终点 (已修改为级联选择) -->
					<u-form-item label="起点" prop="startPoint" borderBottom>
						<view class="picker-item" @click="openAddressPicker('start')">
							<text :class="{'placeholder': !form.startPoint}">{{ form.startPoint || '请选择起点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="终点" prop="endPoint" borderBottom>
						<view class="picker-item" @click="openAddressPicker('end')">
							<text :class="{'placeholder': !form.endPoint}">{{ form.endPoint || '请选择终点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 出发时间 -->
					<u-form-item label="出发时间" prop="timePoint" borderBottom>
						<view class="picker-item" @click="showTimePicker">
							<text :class="{'placeholder': !form.timePoint}">{{ form.timePoint || '请选择出发时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showCarPicker">
							<text :class="{'placeholder': !form.carType}">{{ form.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="form.peopleNum" :min="1" :max="getSelectedCarMaxNum()"
								:disabled="!form.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="form.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="form.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="form.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="form.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="form.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>

			<!-- 拼车表单 -->
			<view v-else-if="currentService === 1">
				<u-form :model="carpoolForm" ref="carpoolForm" labelWidth="160rpx">
					<!-- 起点终点 -->
					<u-form-item label="起点" prop="startPoint" borderBottom>
						<view class="picker-item" @click="openAddressPicker('carpoolStart')">
							<text :class="{'placeholder': !carpoolForm.startPoint}">{{ carpoolForm.startPoint || '请选择起点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="终点" prop="endPoint" borderBottom>
						<view class="picker-item" @click="openAddressPicker('carpoolEnd')">
							<text :class="{'placeholder': !carpoolForm.endPoint}">{{ carpoolForm.endPoint || '请选择终点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 出发时间 -->
					<u-form-item label="出发时间" prop="timePoint" borderBottom>
						<view class="picker-item" @click="showCarpoolTimePicker">
							<text :class="{'placeholder': !carpoolForm.timePoint}">{{ carpoolForm.timePoint || '请选择出发时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showCarpoolCarPicker">
							<text :class="{'placeholder': !carpoolForm.carType}">{{ carpoolForm.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="carpoolForm.peopleNum" :min="1" :max="getSelectedCarpoolCarMaxNum()"
								:disabled="!carpoolForm.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="carpoolForm.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="carpoolForm.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="carpoolForm.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="carpoolForm.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="carpoolForm.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>

			<!-- 接送机表单 -->
			<view v-else>
				<u-form :model="airportForm" ref="airportForm" labelWidth="160rpx">
					<!-- 接机/送机选择 -->
					<u-form-item label="服务类型" prop="serviceType" borderBottom>
						<u-radio-group v-model="airportForm.serviceType" placement="row">
							<u-radio label="接机" name="pickup"></u-radio>
							<u-radio label="送机" name="dropoff"></u-radio>
						</u-radio-group>
					</u-form-item>

					<!-- 接机：到达机场/送机：起点 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '到达机场' : '起点'" prop="location"
						borderBottom>
						<template v-if="airportForm.serviceType === 'pickup'">
							<view class="picker-item" @click="showAirportPicker">
								<text :class="{'placeholder': !airportForm.airport}">{{ airportForm.airport || '请选择机场' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
						<template v-else>
							<view class="picker-item" @click="openAddressPicker('airportStart')">
								<text :class="{'placeholder': !airportForm.startPoint}">{{ airportForm.startPoint || '请选择起点地址' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
					</u-form-item>

					<!-- 接机：终点/送机：到达机场 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '终点' : '到达机场'" prop="destination"
						borderBottom>
						<template v-if="airportForm.serviceType === 'pickup'">
							<view class="picker-item" @click="openAddressPicker('airportEnd')">
								<text :class="{'placeholder': !airportForm.endPoint}">{{ airportForm.endPoint || '请选择终点地址' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
						<template v-else>
							<view class="picker-item" @click="showAirportPicker">
								<text :class="{'placeholder': !airportForm.airport}">{{ airportForm.airport || '请选择机场' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
					</u-form-item>

					<!-- 航班号 -->
					<u-form-item label="航班号" prop="flightNumber" borderBottom>
						<u-input v-model="airportForm.flightNumber" placeholder="请输入航班号" border="none"></u-input>
					</u-form-item>

					<!-- 接机：到达时间/送机：出发时间 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '到达时间' : '出发时间'" prop="time"
						borderBottom>
						<view class="picker-item" @click="showAirportTimePicker">
							<text :class="{'placeholder': !airportForm.time}">{{ airportForm.time || '请选择时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showAirportCarPicker">
							<text :class="{'placeholder': !airportForm.carType}">{{ airportForm.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="airportForm.peopleNum" :min="1" :max="getSelectedAirportCarMaxNum()"
								:disabled="!airportForm.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="airportForm.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="airportForm.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="airportForm.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="airportForm.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="airportForm.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>
		</view>

		<!-- 底部价格和提交 -->
		<view class="bottom-bar">
			<view class="price">联系客服</view>
			<view style="width: 30vw;">
				<u-button type="primary" text="提交订单" @click="submitOrder"></u-button>
			</view>
		</view>

		<!-- 调试按钮 - 仅开发环境使用 -->
		<!-- #ifdef H5 -->
		<view style="position: fixed; top: 10px; right: 10px; z-index: 9999;">
			<u-button size="mini" type="warning" text="测试地址" @click="testAddressData"></u-button>
		</view>
		<!-- #endif -->

		<!-- 客服二维码弹窗 -->
		<u-modal :show="showQrCodePopup" title="客服微信" @confirm="showQrCodePopup = false"
			@close="showQrCodePopup = false">
			<view class="qr-code-popup">
				<image :show-menu-by-longpress="true" src="/static/qrcode.png" mode="aspectFit"></image>
			</view>
		</u-modal>

		<!-- 统一地址级联选择器 (新增) -->
		<u-picker 
			:show="showAddressPicker" 
			:columns="addressPickerColumns" 
			keyName="label"
			ref="addressPicker"
			@change="onAddressPickerChange"
			@confirm="onAddressPickerConfirm"
			@cancel="showAddressPicker = false"
		></u-picker>

		<!-- 时间选择器弹窗 -->
		<u-datetime-picker :show="showTimePickerVisible" v-model="currentDate" mode="datetime" @confirm="onTimeConfirm"
			@cancel="showTimePickerVisible = false"></u-datetime-picker>

		<!-- 车辆选择器弹窗 -->
		<u-picker :show="showCarPickerVisible" :columns="[carList]" keyName="carName" @confirm="onCarConfirm"
			@cancel="showCarPickerVisible = false"></u-picker>

		<!-- 机场选择器弹窗 -->
		<u-picker :show="showAirportPickerVisible" :columns="[airportList]" @confirm="onAirportConfirm"
			@cancel="showAirportPickerVisible = false"></u-picker>

		<!-- 机场时间选择器弹窗 -->
		<u-datetime-picker :show="showAirportTimePickerVisible" v-model="currentDate" mode="datetime"
			@confirm="onAirportTimeConfirm" @cancel="showAirportTimePickerVisible = false"></u-datetime-picker>

		<!-- 机场车辆选择器弹窗 -->
		<u-picker :show="showAirportCarPickerVisible" :columns="[carList]" keyName="carName" @confirm="onAirportCarConfirm"
			@cancel="showAirportCarPickerVisible = false"></u-picker>

		<!-- 拼车时间选择器弹窗 -->
		<u-datetime-picker :show="showCarpoolTimePickerVisible" v-model="currentDate" mode="datetime"
			@confirm="onCarpoolTimeConfirm" @cancel="showCarpoolTimePickerVisible = false"></u-datetime-picker>

		<!-- 拼车车辆选择器弹窗 -->
		<u-picker :show="showCarpoolCarPickerVisible" :columns="[carList]" keyName="carName" @confirm="onCarpoolCarConfirm"
			@cancel="showCarpoolCarPickerVisible = false"></u-picker>

	</view>
</template>

<script>
	import {
		listCarType
	} from "@/api/lease/carType.js"
	import {
		baseUrl
	} from "../config";
	import {
		listSwiper
	} from "@/api/lease/swiper.js"
	import {
		addCarOrder
	} from "@/api/lease/carOrder.js"
	import {
		listAirport
	} from "@/api/lease/airport.js"
	import {
		listStarAddress
	} from "@/api/lease/starAddress.js"
	import {
		listEndAddress
	} from "@/api/lease/endAddress.js"
	export default {
		data() {
			return {
				currentDate: Number(new Date()),
				baseUrl,
				noticeText: "欢迎使用租车服务，祝您旅途愉快！",
				swiperList: [], // 轮播图数据
				serviceTypes: [{
						name: '包车'
					},
					{
						name: '拼车'
					},
					{
						name: '接送机'
					}
				],
				currentService: 0,
				carList: [],
				airportList: [],

				// --- 地址选择相关新状态 ---
				startAddressData: [], // 存储起点地址的树形结构
				endAddressData: [], // 存储终点地址的树形结构
				showAddressPicker: false, // 控制统一地址选择器的显示
				addressPickerColumns: [], // 地址选择器的数据
				currentPickerType: '', // 标记当前是为哪个字段选择地址
				// --- 地址选择相关新状态 End ---

				form: {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				},
				showQrCodePopup: false,
				showTimePickerVisible: false,
				showCarPickerVisible: false,
				showAirportPickerVisible: false,
				showAirportTimePickerVisible: false,
				showAirportCarPickerVisible: false,
				showCarpoolTimePickerVisible: false,
				showCarpoolCarPickerVisible: false,
				carpoolForm: {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				},
				airportForm: {
					serviceType: 'pickup',
					airport: '',
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					flightNumber: '',
					time: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				}
			}
		},
		onLoad() {
			this.getSwiperList()
			this.getCarTypeList()
			this.getAirportList()
			// 初始化地址数据 - 确保立即可用
			this.setTestAddressData(); // 先设置测试数据确保立即可用
			this.initAddressData() // 然后异步加载真实数据
			this.$store.dispatch('h5Test').then(res => {
			  this.loginSuccess()
			})
		},
		methods: {
			// 登录成功后，处理函数
			loginSuccess(result) {
			  // 设置用户信息
			  this.$store.dispatch('GetInfo').then(res => {
			  })
			},

			// 测试地址数据方法
			testAddressData() {
				console.log('=== 地址数据测试 ===');
				console.log('起点数据长度:', this.startAddressData.length);
				console.log('终点数据长度:', this.endAddressData.length);
				console.log('起点数据:', this.startAddressData);
				console.log('终点数据:', this.endAddressData);

				uni.showModal({
					title: '地址数据状态',
					content: `起点数据: ${this.startAddressData.length}条\n终点数据: ${this.endAddressData.length}条`,
					showCancel: false
				});
			},
			
			
			getAirportList() {
				listAirport({
					pageSize: 100000
				}).then(res => {
					this.airportList = res.rows.map(item => item.airportName)
				})
			},

			/**
			 * @description 初始化所有地址数据，获取扁平列表并转换为树形结构
			 */
			initAddressData() {
				console.log('开始初始化地址数据...');

				// 先设置测试数据，确保有数据可用
				this.setTestAddressData();
				console.log('测试数据设置完成，起点数据长度:', this.startAddressData.length, '终点数据长度:', this.endAddressData.length);

				// 获取起点地址
				listStarAddress({
					pageSize: 1000000
				}).then(res => {
					console.log('起点地址API响应:', res);
					if (res.rows && res.rows.length > 0) {
						this.startAddressData = this.handleTree(res.rows, 'addrId', 'parentId');
						console.log('起点地址树形数据设置完成，长度:', this.startAddressData.length);
					} else {
						console.log('起点地址API返回数据为空，继续使用测试数据');
					}
				}).catch(err => {
					console.error('获取起点地址失败，继续使用测试数据:', err);
				});

				// 获取终点地址
				listEndAddress({
					pageSize: 1000000
				}).then(res => {
					console.log('终点地址API响应:', res);
					if (res.rows && res.rows.length > 0) {
						this.endAddressData = this.handleTree(res.rows, 'addrId', 'parentId');
						console.log('终点地址树形数据设置完成，长度:', this.endAddressData.length);
					} else {
						console.log('终点地址API返回数据为空，继续使用测试数据');
					}
				}).catch(err => {
					console.error('获取终点地址失败，继续使用测试数据:', err);
				});
			},

			/**
			 * @description 设置测试地址数据
			 */
			setTestAddressData() {
				const testData = [
					{
						addrId: 100,
						parentId: 0,
						addrName: "珠海区",
						label: "珠海区",
						value: 100,
						children: [
							{
								addrId: 101,
								parentId: 100,
								addrName: "高铁站",
								label: "高铁站",
								value: 101,
								children: []
							},
							{
								addrId: 102,
								parentId: 100,
								addrName: "珠海金湾机场",
								label: "珠海金湾机场",
								value: 102,
								children: []
							}
						]
					},
					{
						addrId: 103,
						parentId: 0,
						addrName: "港珠澳地区",
						label: "港珠澳地区",
						value: 103,
						children: [
							{
								addrId: 104,
								parentId: 103,
								addrName: "港珠澳P1停车场",
								label: "港珠澳P1停车场",
								value: 104,
								children: []
							}
						]
					}
				];

				this.startAddressData = [...testData]; // 使用展开运算符确保数据独立
				this.endAddressData = [...testData];
				console.log('设置测试地址数据完成，起点数据长度:', this.startAddressData.length, '终点数据长度:', this.endAddressData.length);
				console.log('起点数据详情:', this.startAddressData);
				console.log('终点数据详情:', this.endAddressData);
			},

			/**
			 * @description 将扁平数组转换为树形结构，并添加u-picker所需的label/value
			 */
			handleTree(list, idFieldName = 'id', parentIdFieldName = 'parentId', rootParentId = 0) {
				const tree = [];
				const map = {};

				// 第一次遍历：创建节点映射，并添加label/value/children属性
				list.forEach(item => {
					map[item[idFieldName]] = {
						...item,
						label: item.addrName, // u-picker显示的文本
						value: item.addrId, // u-picker内部的值
						children: []
					};
				});

				// 第二次遍历：构建父子关系
				list.forEach(item => {
					const node = map[item[idFieldName]];
					const parent = map[item[parentIdFieldName]];
					if (parent) {
						parent.children.push(node);
					} else if (item[parentIdFieldName] === rootParentId) {
						tree.push(node);
					}
				});

				return tree;
			},

			/**
			 * @description 打开统一的地址选择器
			 * @param {string} type - 标记当前选择的字段类型 ('start', 'end', 'carpoolStart', etc.)
			 */
			openAddressPicker(type) {
				console.log('openAddressPicker 被调用，类型:', type);
				console.log('当前起点数据长度:', this.startAddressData.length);
				console.log('当前终点数据长度:', this.endAddressData.length);

				this.currentPickerType = type;

				// 确保数据存在
				if (!this.startAddressData || this.startAddressData.length === 0 ||
					!this.endAddressData || this.endAddressData.length === 0) {
					console.log('地址数据不完整，重新设置测试数据');
					this.setTestAddressData();
				}

				// 简化逻辑：直接检查数据并继续
				console.log('type详细信息:', {
					value: type,
					type: typeof type,
					length: type.length,
					includes_start: type.includes('start'),
					includes_Start: type.includes('Start'),
					indexOf_start: type.indexOf('start'),
					indexOf_Start: type.indexOf('Start')
				});

				const isStartType = type.toLowerCase().includes('start'); // 转换为小写再判断
				const isEndType = type.toLowerCase().includes('end'); // 转换为小写再判断

				console.log('类型检查 - isStartType:', isStartType, 'isEndType:', isEndType);

				if (!isStartType && !isEndType) {
					console.log('未知的类型:', type);
					uni.showToast({
						title: '未知的地址类型',
						icon: 'none'
					});
					return;
				}

				// 直接使用数据，不通过中间变量
				const currentData = isStartType ? this.startAddressData : this.endAddressData;
				console.log('当前使用的数据长度:', currentData.length);

				if (!currentData || currentData.length === 0) {
					uni.showToast({
						title: '地址数据加载失败，请重试',
						icon: 'none'
					});
					return;
				}

				// 准备Picker的列数据
				const firstColumn = currentData;
				console.log('firstColumn长度:', firstColumn.length);

				// 智能设置第二列的初始数据
				let secondColumn = [];
				let defaultFirstIndex = 0;

				// 尝试根据当前已选择的地址来设置默认选中项
				const currentAddress = this.getCurrentAddressByType(type);
				if (currentAddress) {
					// 解析当前地址，找到对应的一级地址
					const parts = currentAddress.split('-');
					if (parts.length >= 1) {
						const cityName = parts[0];
						const cityIndex = firstColumn.findIndex(item => item.label === cityName || item.addrName === cityName);
						if (cityIndex >= 0) {
							defaultFirstIndex = cityIndex;
							secondColumn = firstColumn[cityIndex]?.children || [];
						}
					}
				}

				// 如果没有找到匹配的，使用第一个一级地址的子项目
				if (secondColumn.length === 0) {
					secondColumn = firstColumn[0]?.children || [];
				}

				this.addressPickerColumns = [firstColumn, secondColumn];

				console.log('设置地址选择器数据:', {
					type,
					currentAddress,
					defaultFirstIndex,
					firstColumn: firstColumn.map(item => item.label || item.addrName),
					secondColumn: secondColumn.map(item => item.label || item.addrName)
				});

				this.showAddressPicker = true;

				// 设置默认选中项（需要在下一个tick中执行，确保选择器已经渲染）
				this.$nextTick(() => {
					if (this.$refs.addressPicker && this.$refs.addressPicker.setIndexs && defaultFirstIndex > 0) {
						this.$refs.addressPicker.setIndexs([defaultFirstIndex, 0]);
					}
				});
			},

			/**
			 * @description 根据选择器类型获取当前已选择的地址
			 */
			getCurrentAddressByType(type) {
				switch (type) {
					case 'start':
						return this.form.startPoint;
					case 'end':
						return this.form.endPoint;
					case 'carpoolStart':
						return this.carpoolForm.startPoint;
					case 'carpoolEnd':
						return this.carpoolForm.endPoint;
					case 'airportStart':
						return this.airportForm.startPoint;
					case 'airportEnd':
						return this.airportForm.endPoint;
					default:
						return '';
				}
			},

			/**
			 * @description 地址选择器列滚动时的联动处理
			 */
			onAddressPickerChange(e) {
				console.log('地址选择器变化事件:', e);

				// 防止事件对象为空
				if (!e) {
					console.warn('地址选择器事件对象为空');
					return;
				}

				// 安全地获取事件数据，兼容不同的事件格式
				let columnIndex, value;

				// 尝试多种可能的事件格式
				if (e.detail && typeof e.detail === 'object') {
					// uView 2.x 的事件对象在 e.detail
					columnIndex = e.detail.columnIndex;
					value = e.detail.value;
				} else if (typeof e.columnIndex !== 'undefined') {
					// 直接从事件对象获取
					columnIndex = e.columnIndex;
					value = e.value;
				} else {
					// 如果都没有，尝试从其他可能的属性获取
					console.warn('无法从事件对象中获取columnIndex和value:', e);
					return;
				}

				console.log('解析得到 - columnIndex:', columnIndex, 'value:', value);

				// 确保 columnIndex 和 value 都有效
				if (typeof columnIndex !== 'number' || !Array.isArray(value)) {
					console.warn('columnIndex或value格式不正确:', { columnIndex, value });
					return;
				}

				// 只处理第一列（城市）的变化
				if (columnIndex === 0 && value.length > 0) {
					const cityNode = value[0]; // value是完整的节点对象
					console.log('选中的城市节点:', cityNode);

					if (cityNode && Array.isArray(cityNode.children) && cityNode.children.length > 0) {
						// 动态设置第二列的数据
						console.log('设置第二列数据:', cityNode.children);
						try {
							if (this.$refs.addressPicker && typeof this.$refs.addressPicker.setColumnValues === 'function') {
								this.$refs.addressPicker.setColumnValues(1, cityNode.children);
							} else {
								console.warn('addressPicker ref 或 setColumnValues 方法不可用');
							}
						} catch (error) {
							console.error('设置第二列数据时出错:', error);
						}
					} else {
						console.log('城市节点没有子级数据或children为空');
					}
				}
			},

			/**
			 * @description 用户确认地址选择
			 */
			onAddressPickerConfirm(e) {
				console.log('地址选择器确认事件:', e);

				// 防止事件对象为空
				if (!e) {
					console.warn('地址选择器确认事件对象为空');
					return;
				}

				// 安全地获取选择的值
				let value;

				if (e.detail && Array.isArray(e.detail.value)) {
					// uView 2.x 的事件对象在 e.detail
					value = e.detail.value;
				} else if (Array.isArray(e.value)) {
					// 直接从事件对象获取
					value = e.value;
				} else {
					console.warn('无法从事件对象中获取value:', e);
					return;
				}

				console.log('解析得到的选择值:', value);

				// 确保 value 是数组且有数据
				if (!Array.isArray(value) || value.length === 0) {
					console.warn('选择值格式不正确或为空:', value);
					return;
				}

				// 安全地获取城市和区域信息
				const city = (value[0] && typeof value[0] === 'object') ? (value[0].label || value[0].addrName || '') : '';
				const district = (value[1] && typeof value[1] === 'object') ? (value[1].label || value[1].addrName || '') : '';

				console.log('解析得到 - city:', city, 'district:', district);

				const displayText = district ? `${city}-${district}` : city;
				const pointName = district || city; // 最终用于提交的地点名

				console.log('最终结果 - displayText:', displayText, 'pointName:', pointName);

				// 根据当前选择类型，更新对应的表单模型
				switch (this.currentPickerType) {
					case 'start':
						this.form.startPoint = displayText;
						this.form.startPointName = pointName;
						console.log('设置包车起点:', displayText);
						break;
					case 'end':
						this.form.endPoint = displayText;
						this.form.endPointName = pointName;
						console.log('设置包车终点:', displayText);
						break;
					case 'carpoolStart':
						this.carpoolForm.startPoint = displayText;
						this.carpoolForm.startPointName = pointName;
						console.log('设置拼车起点:', displayText);
						break;
					case 'carpoolEnd':
						this.carpoolForm.endPoint = displayText;
						this.carpoolForm.endPointName = pointName;
						console.log('设置拼车终点:', displayText);
						break;
					case 'airportStart':
						this.airportForm.startPoint = displayText;
						this.airportForm.startPointName = pointName;
						console.log('设置接送机起点:', displayText);
						break;
					case 'airportEnd':
						this.airportForm.endPoint = displayText;
						this.airportForm.endPointName = pointName;
						console.log('设置接送机终点:', displayText);
						break;
					default:
						console.warn('未知的选择器类型:', this.currentPickerType);
						break;
				}

				this.showAddressPicker = false;
			},

			onServiceChange(e) {
				this.currentService = e.index;
			},
			getCarTypeList() {
				listCarType({
					pageSize: 100000,
					status: 1
				}).then(res => {
					this.carList = res.rows
				})
			},
			getSwiperList() {
				listSwiper({
					pageSize: 100000
				}).then(res => {
					this.swiperList = res.rows.map(item => this.baseUrl + item.pic)
				})
			},
			showTimePicker() {
				this.showTimePickerVisible = true;
			},
			showCarPicker() {
				this.showCarPickerVisible = true;
			},
			onTimeConfirm(e) {
				// 将时间戳转换为 yyyy-MM-dd HH:mm:ss 格式
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.form.timePoint = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showTimePickerVisible = false;
			},
			onCarConfirm(e) {
				this.form.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.form.peopleNum > e.value[0].maxNum) {
					this.form.peopleNum = 1;
				}
				this.showCarPickerVisible = false;
			},
			showQrCode() {
				this.showQrCodePopup = true;
			},
			submitOrder() {
				uni.showLoading({
					title: "请稍后……",
					mask: true
				})
				// 对包车和接送机表单项进行非空校验
				const phoneReg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
				if (this.currentService === 0) {
					const f = this.form;
					if (!f.startPoint) {
						uni.showToast({
							title: '请输入起点地址',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.endPoint) {
						uni.showToast({
							title: '请输入终点地址',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.timePoint) {
						uni.showToast({
							title: '请选择出发时间',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.peopleNum) {
						uni.showToast({
							title: '请输入乘车人数',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!phoneReg.test(f.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!f.documents || f.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					const params = {
						serviceType: 'charter',
						startPoint: f.startPoint,
						endPoint: f.endPoint,
						timePoint: f.timePoint,
						carType: f.carType,
						peopleNum: f.peopleNum,
						contactName: f.contactName,
						contactPhone: f.contactPhone,
						luggageCount: f.luggageCount,
						documents: f.documents.join(','),
						remark: f.remark,
						startPointName: f.startPointName,
						endPointName: f.endPointName,
						lineId: f.lineId
					};
					// 调用包车下单接口
					addCarOrder(params)
						.then((orderRes) => {
							uni.hideLoading();
							// 清空表单
							this.clearCharterForm();
							// 弹出跳转确认弹框
							uni.showModal({
								title: '提交成功',
								content: '包车订单提交成功，是否跳转到订单页面查看？',
								confirmText: '是',
								cancelText: '否',
								success: (res) => {
									if (res.confirm) {
										// 用户点击"是"，跳转到订单页面
										uni.navigateTo({
											url: '/pages/mine/carOrder/carOrder'
										});
									}
									// 用户点击"否"或取消，保留在首页，不做任何操作
								}
							});
						})
						.catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '包车订单提交失败，请重试',
								icon: 'none'
							});
						});
				} else if (this.currentService === 1) {
					// 拼车表单验证
					const c = this.carpoolForm;
					if (!c.startPoint) {
						uni.showToast({
							title: '请输入起点地址',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.endPoint) {
						uni.showToast({
							title: '请输入终点地址',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.timePoint) {
						uni.showToast({
							title: '请选择出发时间',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.peopleNum) {
						uni.showToast({
							title: '请输入乘车人数',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!phoneReg.test(c.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!c.documents || c.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					const params = {
						serviceType: 'carpooling',
						startPoint: c.startPoint,
						endPoint: c.endPoint,
						timePoint: c.timePoint,
						carType: c.carType,
						peopleNum: c.peopleNum,
						contactName: c.contactName,
						contactPhone: c.contactPhone,
						luggageCount: c.luggageCount,
						documents: c.documents.join(','),
						remark: c.remark,
						startPointName: c.startPointName,
						endPointName: c.endPointName,
						lineId: c.lineId
					};
					// 调用拼车下单接口
					addCarOrder(params)
						.then((orderRes) => {
							uni.hideLoading();
							// 清空表单
							this.clearCarpoolForm();
							// 弹出跳转确认弹框
							uni.showModal({
								title: '提交成功',
								content: '拼车订单提交成功，是否跳转到订单页面查看？',
								confirmText: '是',
								cancelText: '否',
								success: (res) => {
									if (res.confirm) {
										// 用户点击"是"，跳转到订单页面
										uni.navigateTo({
											url: '/pages/mine/carOrder/carOrder'
										});
									}
									// 用户点击"否"或取消，保留在首页，不做任何操作
								}
							});
						})
						.catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '拼车订单提交失败，请重试',
								icon: 'none'
							});
						});
				} else {
					const a = this.airportForm;
					if (!a.serviceType) {
						uni.showToast({
							title: '请选择服务类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (a.serviceType === 'pickup') {
						if (!a.airport) {
							uni.showToast({
								title: '请选择到达机场',
								icon: 'none'
							});
							uni.hideLoading();
							return;
						}
						if (!a.endPoint) {
							uni.showToast({
								title: '请输入终点地址',
								icon: 'none'
							});
							uni.hideLoading();
							return;
						}
					} else {
						if (!a.startPoint) {
							uni.showToast({
								title: '请输入起点地址',
								icon: 'none'
							});
							uni.hideLoading();
							return;
						}
						if (!a.airport) {
							uni.showToast({
								title: '请选择到达机场',
								icon: 'none'
							});
							uni.hideLoading();
							return;
						}
					}
					if (!a.flightNumber) {
						uni.showToast({
							title: '请输入航班号',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.time) {
						uni.showToast({
							title: a.serviceType === 'pickup' ? '请选择到达时间' : '请选择出发时间',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!phoneReg.test(a.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (!a.documents || a.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					const params = {
						serviceType: a.serviceType,
						airport: a.airport,
						startPoint: a.startPoint,
						endPoint: a.endPoint,
						flightNumber: a.flightNumber,
						time: a.time,
						carType: a.carType,
						peopleNum: a.peopleNum,
						contactName: a.contactName,
						contactPhone: a.contactPhone,
						luggageCount: a.luggageCount,
						documents: a.documents.join(','),
						remark: a.remark,
						lineId: a.lineId,
						startPointName: a.startPointName,
						endPointName: a.endPointName,
					};
					uni.hideLoading()
					uni.showToast({
						title: "开发中",
						icon: "none"
					})
				}
			},
			showAirportPicker() {
				this.showAirportPickerVisible = true;
			},
			showAirportTimePicker() {
				this.showAirportTimePickerVisible = true;
			},
			showAirportCarPicker() {
				this.showAirportCarPickerVisible = true;
			},
			onAirportConfirm(e) {
				this.airportForm.airport = e.value[0];
				this.showAirportPickerVisible = false;
			},
			onAirportTimeConfirm(e) {
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.airportForm.time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showAirportTimePickerVisible = false;
			},
			onAirportCarConfirm(e) {
				this.airportForm.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.airportForm.peopleNum > e.value[0].maxNum) {
					this.airportForm.peopleNum = 1;
				}
				this.showAirportCarPickerVisible = false;
			},
			// 拼车相关方法
			showCarpoolTimePicker() {
				this.showCarpoolTimePickerVisible = true;
			},
			showCarpoolCarPicker() {
				this.showCarpoolCarPickerVisible = true;
			},
			onCarpoolTimeConfirm(e) {
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.carpoolForm.timePoint = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showCarpoolTimePickerVisible = false;
			},
			onCarpoolCarConfirm(e) {
				this.carpoolForm.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.carpoolForm.peopleNum > e.value[0].maxNum) {
					this.carpoolForm.peopleNum = 1;
				}
				this.showCarpoolCarPickerVisible = false;
			},
			getSelectedCarMaxNum() {
				if (!this.form.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.form.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},
			getSelectedAirportCarMaxNum() {
				if (!this.airportForm.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.airportForm.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},
			getSelectedCarpoolCarMaxNum() {
				if (!this.carpoolForm.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.carpoolForm.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},

			// 清空包车表单
			clearCharterForm() {
				this.form = {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			},
			// 清空拼车表单
			clearCarpoolForm() {
				this.carpoolForm = {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			},
			// 清空接送机表单
			clearAirportForm() {
				this.airportForm = {
					serviceType: 'pickup',
					airport: '',
					startPoint: '',
					endPoint: '',
					flightNumber: '',
					time: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			}
		}
	}
</script>


<style lang="scss">
	.container {
		padding: 20rpx;
		/* #ifdef H5 */
		padding-bottom: 180rpx; /* H5环境下为底部栏和tabBar留出空间 */
		/* #endif */
		/* #ifndef H5 */
		padding-bottom: calc(180rpx + constant(safe-area-inset-bottom)); /* 小程序环境下额外考虑安全区 */
		padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
		/* #endif */
	}

	.swiper-box {
		margin: 20rpx 0;
	}

	.form-container {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-top: 20rpx;

		:deep(.u-form-item) {
			.u-form-item__body {
				.u-form-item__body__left {
					min-width: 160rpx; // 增加label宽度
				}
			}
		}
	}

	.picker-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%; // 确保占满宽度
		padding: 10rpx 0; // 调整内边距

		text {
			color: #333;
			font-size: 28rpx;
			// 增加placeholder样式
			&.placeholder {
				color: #c0c4cc;
			}
		}
	}

	.bottom-bar {
		position: fixed;
		/* #ifdef H5 */
		bottom: 50px; /* H5环境下为tabBar留出空间 */
		/* #endif */
		/* #ifndef H5 */
		bottom: calc(50px + constant(safe-area-inset-bottom)); /* 小程序环境下为tabBar和安全区留出空间 */
		bottom: calc(50px + env(safe-area-inset-bottom));
		/* #endif */
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 998; /* 降低z-index，确保不会覆盖tabBar */


		.price {
			font-size: 32rpx;
			color: #f56c6c;
			font-weight: bold;
		}
	}

	.qr-code-popup {
		padding: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 400rpx;
			height: 400rpx;
		}
	}

	:deep(.u-checkbox-group) {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.hint-text {
		font-size: 24rpx;
		color: #999;
		margin: 10rpx 0;
	}
</style>