<template>
  <view class="privacy-container">
    <view class="privacy-title">隐私协议</view>
    <view class="privacy-content">
      <view class="section">
        <view class="section-title">1. 信息收集</view>
        <view class="section-content">
          我们收集的信息包括但不限于：
          <view class="item">• 微信授权的基本信息（头像、昵称等）</view>
          <view class="item">• 手机号码</view>
          <view class="item">• 位置信息</view>
          <view class="item">• 设备信息</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">2. 信息使用</view>
        <view class="section-content">
          我们使用收集的信息用于：
          <view class="item">• 提供租车服务</view>
          <view class="item">• 处理订单和支付</view>
          <view class="item">• 发送服务通知</view>
          <view class="item">• 改进服务质量</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">3. 信息保护</view>
        <view class="section-content">
          我们采取严格的数据安全措施保护您的个人信息：
          <view class="item">• 数据加密存储</view>
          <view class="item">• 访问权限控制</view>
          <view class="item">• 定期安全审计</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">4. 信息共享</view>
        <view class="section-content">
          我们不会向第三方出售您的个人信息。仅在以下情况下共享信息：
          <view class="item">• 获得您的明确同意</view>
          <view class="item">• 法律法规要求</view>
          <view class="item">• 维护平台安全</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">5. 用户权利</view>
        <view class="section-content">
          您有权：
          <view class="item">• 访问您的个人信息</view>
          <view class="item">• 更正不准确的信息</view>
          <view class="item">• 删除您的账户</view>
          <view class="item">• 撤回授权同意</view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">6. 协议更新</view>
        <view class="section-content">
          我们可能会不时更新本隐私协议。更新后，我们会在小程序内通知您。继续使用我们的服务即表示您同意更新后的协议。
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  }
}
</script>

<style lang="scss">
.privacy-container {
  padding: 30rpx;
  background-color: #fff;
  min-height: 100vh;

  .privacy-title {
    font-size: 36rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 40rpx;
    color: #333;
  }

  .privacy-content {
    .section {
      margin-bottom: 40rpx;

      .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }

      .section-content {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;

        .item {
          margin: 10rpx 0;
          padding-left: 20rpx;
        }
      }
    }
  }
}
</style> 