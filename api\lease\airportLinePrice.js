import request from '@/utils/request'

// 查询接机路线价格列表
export function listAirportLinePrice(query) {
  return request({
    url: '/lease/airportLinePrice/list',
    method: 'get',
    params: query
  })
}

// 查询接机路线价格详细
export function getAirportLinePrice(id) {
  return request({
    url: '/lease/airportLinePrice/' + id,
    method: 'get'
  })
}

// 新增接机路线价格
export function addAirportLinePrice(data) {
  return request({
    url: '/lease/airportLinePrice',
    method: 'post',
    data: data
  })
}

// 修改接机路线价格
export function updateAirportLinePrice(data) {
  return request({
    url: '/lease/airportLinePrice',
    method: 'put',
    data: data
  })
}

// 删除接机路线价格
export function delAirportLinePrice(id) {
  return request({
    url: '/lease/airportLinePrice/' + id,
    method: 'delete'
  })
}
