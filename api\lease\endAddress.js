import request from '@/utils/request'

// 查询目的地地址列表
export function listEndAddress(query) {
  return request({
    url: '/lease/endAddress/list',
    method: 'get',
    params: query
  })
}

// 查询目的地地址详细
export function getEndAddress(addrId) {
  return request({
    url: '/lease/endAddress/' + addrId,
    method: 'get'
  })
}

// 新增目的地地址
export function addEndAddress(data) {
  return request({
    url: '/lease/endAddress',
    method: 'post',
    data: data
  })
}

// 修改目的地地址
export function updateEndAddress(data) {
  return request({
    url: '/lease/endAddress',
    method: 'put',
    data: data
  })
}

// 删除目的地地址
export function delEndAddress(addrId) {
  return request({
    url: '/lease/endAddress/' + addrId,
    method: 'delete'
  })
}
