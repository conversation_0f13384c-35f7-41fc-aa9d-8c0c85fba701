<template>
  <view class="login-container">
    <view class="logo-content align-center justify-center flex">
      <image style="width: 200rpx;height: 200rpx;" :src="globalConfig.appInfo.logo" mode="widthFix">
      </image>
      <text class="title">租车小程序</text>
    </view>
    
    <view class="login-form-content">
      <view class="agreement-box">
        <checkbox-group @change="checkboxChange">
          <label class="agreement-label">
            <checkbox :checked="isAgree" color="#2979ff" style="transform:scale(0.7)" />
            <text class="agreement-text">我已阅读并同意</text>
            <text class="agreement-link" @click="handlePrivacy">《隐私协议》</text>
          </label>
        </checkbox-group>
      </view>
      
      <view class="action-btn">
        <button type="primary" open-type="getPhoneNumber" @getphonenumber="WeChatLogin" 
          :disabled="!isAgree" :class="{'btn-disabled': !isAgree}">手机号快捷登录</button>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        isAgree: false,
        globalConfig: getApp().globalData.config
      }
    },
    methods: {
      checkboxChange(e) {
        this.isAgree = e.detail.value.length > 0
      },
      // 隐私协议
      handlePrivacy() {
        this.$tab.navigateTo('/pages/privacy')
      },
      WeChatLogin(e) {
        if (!this.isAgree) {
          this.$modal.msgError('请先阅读并同意隐私协议')
          return
        }
        
        this.$modal.loading("登录中，请耐心等待...")
        let params = {}
        params.code = e.detail.code
        uni.getUserInfo({
          success: (res) => {
            params.avatarUrl = res.userInfo.avatarUrl
            params.nickName = res.userInfo.nickName
            wx.login({
              success: (logRes) => {
                params.code2 = logRes.code
              },
              complete: () => {
                this.$store.dispatch('WxLogin', params).then(res => {
                  this.loginSuccess()
                })
              }
            })
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .login-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 40rpx;

    .logo-content {
      width: 100%;
      font-size: 36rpx;
      text-align: center;
      padding-top: 20%;
      flex-direction: column;

      image {
        border-radius: 20rpx;
        margin-bottom: 30rpx;
      }

      .title {
        font-size: 48rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .login-form-content {
      width: 100%;
      margin-top: 15%;

      .agreement-box {
        margin-bottom: 60rpx;
        
        .agreement-label {
          display: flex;
          align-items: center;
          justify-content: center;
          
          .agreement-text {
            font-size: 28rpx;
            color: #666;
          }
          
          .agreement-link {
            font-size: 28rpx;
            color: #2979ff;
          }
        }
      }

      .action-btn {
        width: 100%;
        
        button {
          width: 100%;
          height: 90rpx;
          line-height: 90rpx;
          border-radius: 45rpx;
          font-size: 32rpx;
          background: #2979ff;
          
          &.btn-disabled {
            background: #cccccc;
            color: #ffffff;
          }
        }
      }
    }
  }
</style>
