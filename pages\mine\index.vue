<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <u-icon name="account" size="40" color="#909399"></u-icon>
          </view>
          <image v-if="avatar" @click="handleToAvatar" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              用户名：{{ name }}
            </view>
          </view>
        </view>
        <view @click="handleToInfo" class="flex align-center">
          <text>个人信息</text>
          <u-icon name="arrow-right" size="20" color="#ffffff"></u-icon>
        </view>
      </view>
    </view>

    <view class="content-section">

      <view class="menu-list">
        <!-- <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <u-icon name="edit-pen" size="22" class="menu-icon"></u-icon>
            <view>编辑资料</view>
          </view>
        </view> -->
        <view class="list-cell list-cell-arrow">
          <button class="menu-btn" open-type="contact" hover-class="none">
            <view class="menu-item-box">
              <u-icon name="server-man" size="22" class="menu-icon"></u-icon>
              <view>在线客服</view>
            </view>
          </button>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToCarOrders">
          <view class="menu-item-box">
            <u-icon name="car" size="22" class="menu-icon"></u-icon>
            <view>包车订单</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToProductOrders">
          <view class="menu-item-box">
            <u-icon name="order" size="22" class="menu-icon"></u-icon>
            <view>商品订单</view>
          </view>
        </view>
        <!-- <view class="list-cell list-cell-arrow" @click="handleHelp">
          <view class="menu-item-box">
            <u-icon name="question-circle" size="22" class="menu-icon"></u-icon>
            <view>常见问题</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleAbout">
          <view class="menu-item-box">
            <u-icon name="heart" size="22" class="menu-icon"></u-icon>
            <view>关于我们</view>
          </view>
        </view> -->
        <view class="list-cell list-cell-arrow" @click="handleLogout">
          <view class="menu-item-box">
            <u-icon name="share-square" size="22" class="menu-icon"></u-icon>
            <view>退出登录</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  import storage from '@/utils/storage'
  
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        version: getApp().globalData.config.appInfo.version
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      }
    },
    methods: {
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/mine/info/edit')
      },
      handleToCarOrders() {
        uni.navigateTo({
        	url:"/pages/mine/carOrder/carOrder"
        })
      },
      handleToProductOrders() {
        uni.navigateTo({
        	url:"/pages/mine/goodsOrder/goodsOrder"
        })
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleToAvatar() {
        this.$tab.navigateTo('/pages/mine/avatar/index')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {
            this.$tab.reLaunch('/pages/index')
          })
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #f5f6f7;
  }

  .mine-container {
    width: 100%;
    height: 100%;


    .header-section {
      padding: 15px 15px 45px 15px;
      background-color: #3c96f3;
      color: white;

      .login-tip {
        font-size: 18px;
        margin-left: 10px;
      }

      .cu-avatar {
        border: 2px solid #eaeaea;

        .icon {
          font-size: 40px;
        }
      }

      .user-info {
        margin-left: 15px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
        }
      }
    }

    .content-section {
      position: relative;
      top: -50px;

      .mine-actions {
        margin: 15px 15px;
        padding: 20px 0px;
        border-radius: 8px;
        background-color: white;

        .action-item {
          .icon {
            font-size: 28px;
          }

          .text {
            display: block;
            font-size: 13px;
            margin: 8px 0px;
          }
        }
      }
    }
  }

  .menu-btn {
    width: 100%;
    height: 100%;
    background-color: transparent;
    padding: 0;
    margin: 0;
    line-height: inherit;
    border: none;
    text-align: left;
    font-size: inherit;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    
    &::after {
      display: none;
    }
  }
  
  .menu-item-box {
    display: flex;
    align-items: center;
    position: relative;
    
    .menu-icon {
      margin-right: 10px;
    }
    
    .arrow-icon {
      position: absolute;
      right: 0;
    }
  }
</style>
