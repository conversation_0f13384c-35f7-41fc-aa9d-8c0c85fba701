import request from '@/utils/request'

// 查询出发地地址列表
export function listStarAddress(query) {
  return request({
    url: '/lease/starAddress/list',
    method: 'get',
    params: query
  })
}

// 查询出发地地址详细
export function getStarAddress(addrId) {
  return request({
    url: '/lease/starAddress/' + addrId,
    method: 'get'
  })
}

// 新增出发地地址
export function addStarAddress(data) {
  return request({
    url: '/lease/starAddress',
    method: 'post',
    data: data
  })
}

// 修改出发地地址
export function updateStarAddress(data) {
  return request({
    url: '/lease/starAddress',
    method: 'put',
    data: data
  })
}

// 删除出发地地址
export function delStarAddress(addrId) {
  return request({
    url: '/lease/starAddress/' + addrId,
    method: 'delete'
  })
}
