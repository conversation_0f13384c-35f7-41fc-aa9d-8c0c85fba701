import request from '@/utils/request'

// 查询路线价格列表
export function listLinePrice(query) {
  return request({
    url: '/lease/linePrice/list',
    method: 'get',
    params: query
  })
}

// 查询路线价格详细
export function getLinePrice(id) {
  return request({
    url: '/lease/linePrice/' + id,
    method: 'get'
  })
}

// 新增路线价格
export function addLinePrice(data) {
  return request({
    url: '/lease/linePrice',
    method: 'post',
    data: data
  })
}

// 修改路线价格
export function updateLinePrice(data) {
  return request({
    url: '/lease/linePrice',
    method: 'put',
    data: data
  })
}

// 删除路线价格
export function delLinePrice(id) {
  return request({
    url: '/lease/linePrice/' + id,
    method: 'delete'
  })
}
