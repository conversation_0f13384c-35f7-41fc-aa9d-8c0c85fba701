import Vue from 'vue'
import App from './App'
import store from './store' // store
import plugins from './plugins' // plugins
import './permission' // permission
import share from '@/utils/share'
import noti from './utils/notification.js'
import uView from '@/uni_modules/uview-ui'
Vue.use(uView)
Vue.use(plugins)
Vue.mixin(share)
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.prototype.$noti = noti
App.mpType = 'app'

const app = new Vue({
  ...App
})

app.$mount()
