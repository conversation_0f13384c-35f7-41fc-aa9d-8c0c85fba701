import request from '@/utils/request'

// 查询机场列表
export function listAirport(query) {
  return request({
    url: '/lease/airport/list',
    method: 'get',
    params: query
  })
}

// 查询机场详细
export function getAirport(id) {
  return request({
    url: '/lease/airport/' + id,
    method: 'get'
  })
}

// 新增机场
export function addAirport(data) {
  return request({
    url: '/lease/airport',
    method: 'post',
    data: data
  })
}

// 修改机场
export function updateAirport(data) {
  return request({
    url: '/lease/airport',
    method: 'put',
    data: data
  })
}

// 删除机场
export function delAirport(id) {
  return request({
    url: '/lease/airport/' + id,
    method: 'delete'
  })
}
