<template>
	<view class="container" v-if="detail">
		<!-- 轮播图 -->
		<u-swiper
			:list="swiperList"
			height="500"
			:border-radius="0"
			:circular="true"
		></u-swiper>
		
		<!-- 商品信息 -->
		<view class="goods-info">
			<view class="price-box">
				<text class="price">¥{{detail.price}}</text>
			</view>
			<view class="name-box">
				<text class="name">{{detail.name}}</text>
			</view>
		</view>
		
		<!-- 商品详情 -->
		<view class="goods-detail">
			<view class="title">商品详情</view>
			<rich-text :nodes="detail.content"></rich-text>
		</view>
		
		<!-- 底部悬浮栏 -->
		<view class="bottom-bar">
			<view class="price-info">
				<text class="label">价格</text>
				<text class="price">¥{{detail.price}}</text>
			</view>
			<view class="button-group">
				<u-button type="primary" @click="bookNow">立即预定</u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import { baseUrl } from '../../config';
	import { getGoods } from "@/api/lease/goods.js"
	export default {
		data() {
			return {
				baseUrl,
				detail: null,
				swiperList: []
			}
		},
		onLoad({id}) {
			this.getDetail(id)
		},
		methods: {
			getDetail(id){
				getGoods(id).then(res => {
					this.detail = res.data
					// 处理轮播图数据
					if(this.detail && this.detail.pic) {
						this.swiperList = this.detail.pic.split(',').map(item => this.baseUrl + item)
					}
					// 处理富文本中的图片地址
					if(this.detail && this.detail.content) {
						this.detail.content = this.detail.content.replace(/src="([^"]+)"/g, (match, p1) => {
							// 删除dev-api
							const newSrc = p1.replace('/dev-api', '')
							return `src="${this.baseUrl + newSrc}" style="width: 100%; height: 100%;"`
						})
					}
				})
			},
			bookNow() {
				uni.navigateTo({
					url:"/pages/confirmGoodsOrder/confirmGoodsOrder?goodsId="+this.detail.id
				})
			}
		}
	}
</script>

<style lang="scss">
.container {
	/* #ifdef H5 */
	padding-bottom: 150rpx; /* H5环境下为底部悬浮栏和tabBar留出空间 */
	/* #endif */
	/* #ifndef H5 */
	padding-bottom: calc(150rpx + constant(safe-area-inset-bottom)); /* 小程序环境下额外考虑安全区 */
	padding-bottom: calc(150rpx + env(safe-area-inset-bottom));
	/* #endif */
}

.goods-info {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
	
	.price-box {
		margin-bottom: 20rpx;
		
		.price {
			font-size: 48rpx;
			color: #f56c6c;
			font-weight: bold;
		}
	}
	
	.name-box {
		.name {
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
			line-height: 1.4;
		}
	}
}

.goods-detail {
	background-color: #fff;
	padding: 30rpx;
	
	.title {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1px solid #eee;
	}
	
	:deep(img) {
		width: 100% !important;
		height: 100% !important;
		display: block;
	}
}

.bottom-bar {
	position: fixed;
	/* #ifdef H5 */
	bottom: 50px; /* H5环境下为tabBar留出空间 */
	/* #endif */
	/* #ifndef H5 */
	bottom: calc(50px + constant(safe-area-inset-bottom)); /* 小程序环境下为tabBar和安全区留出空间 */
	bottom: calc(50px + env(safe-area-inset-bottom));
	/* #endif */
	left: 0;
	right: 0;
	height: 100rpx;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	z-index: 998; /* 确保不会覆盖tabBar */
	
	.price-info {
		display: flex;
		align-items: baseline;
		
		.label {
			font-size: 24rpx;
			color: #666;
			margin-right: 10rpx;
		}
		
		.price {
			font-size: 36rpx;
			color: #f56c6c;
			font-weight: bold;
		}
	}
	
	.button-group {
		:deep(.u-button) {
			width: 200rpx;
			height: 70rpx;
			font-size: 28rpx;
		}
	}
}
</style>
