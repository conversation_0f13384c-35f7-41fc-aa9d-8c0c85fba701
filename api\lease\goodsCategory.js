import request from '@/utils/request'

// 查询商品分类列表
export function listGoodsCategory(query) {
  return request({
    url: '/lease/goodsCategory/list',
    method: 'get',
    params: query
  })
}

// 查询商品分类详细
export function getGoodsCategory(id) {
  return request({
    url: '/lease/goodsCategory/' + id,
    method: 'get'
  })
}

// 新增商品分类
export function addGoodsCategory(data) {
  return request({
    url: '/lease/goodsCategory',
    method: 'post',
    data: data
  })
}

// 修改商品分类
export function updateGoodsCategory(data) {
  return request({
    url: '/lease/goodsCategory',
    method: 'put',
    data: data
  })
}

// 删除商品分类
export function delGoodsCategory(id) {
  return request({
    url: '/lease/goodsCategory/' + id,
    method: 'delete'
  })
}
