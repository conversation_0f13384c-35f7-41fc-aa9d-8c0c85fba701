<template>
	<view class="container">
		<!-- 商品信息 -->
		<view class="goods-info">
			<image class="goods-img" :src="baseUrl + params.goodsPic.split(',')[0]" mode="aspectFill"></image>
			<view class="goods-detail">
				<view class="goods-name">{{ params.goodsName }}</view>
				<view class="goods-price">￥{{ params.goodsPrice }}</view>
			</view>
			<view class="goods-num">
				<u-number-box v-model="params.goodsNum" :min="1" @change="updatePrice"></u-number-box>
			</view>
		</view>

		<!-- 出行日期 -->
		<view class="form-item">
			<text class="label">出行日期：</text>
			<view class="picker" @click="showDatePicker = true">
				<text>{{ params.startDate || '请选择出发时间' }}</text>
				<u-icon name="arrow-right"></u-icon>
			</view>
		</view>
		<u-datetime-picker
			:show="showDatePicker"
			v-model="dateValue"
			mode="datetime"
			@confirm="onDateConfirm"
			@cancel="showDatePicker = false"
		></u-datetime-picker>

		<!-- 联系人 -->
		<view class="form-item">
			<text class="label">联系人：</text>
			<u-input v-model="params.contactName" placeholder="请填写乘车人姓名" border="none"></u-input>
		</view>
		<!-- 联系电话 -->
		<view class="form-item">
			<text class="label">联系电话：</text>
			<u-input v-model="params.contactPhone" placeholder="请填写乘车人联系方式" border="none"></u-input>
		</view>
		<!-- 备注 -->
		<view class="form-item">
			<text class="label">备注：</text>
			<u-textarea v-model="params.reamark" placeholder="请填写备注内容"></u-textarea>
		</view>

		<!-- 底部悬浮栏 -->
		<view class="bottom-bar">
			<view class="total-price">
				实付：<text class="price">￥{{ params.price }}</text>
			</view>
			<u-button
				type="primary"
				@click="submitOrder"
				class="submit-btn"
				:customStyle="{
					width: '180rpx',
					height: '50rpx',
					fontSize: '24rpx'
				}"
			>
				立即预定
			</u-button>
		</view>
	</view>
</template>

<script>
	import { addGoodsOrder } from "@/api/lease/goodsOrder.js"
	import { getGoods } from "@/api/lease/goods.js"
	import { baseUrl } from "@/config"
	export default {
		data() {
			return {
				params:{
					goodsId:'',
					goodsName:'',
					goodsPic:'',
					goodsNum:1,
					goodsPrice:'',
					price:'',
					startDate:'',
					contactName:'',
					contactPhone:'',
					reamark:''
				},
				baseUrl,
				showDatePicker: false,
				dateValue: Number(new Date())
			}
		},
		onLoad({goodsId}) {
			this.getGoodsDetail(goodsId)
		},
		methods: {
			getGoodsDetail(id){
				getGoods(id).then(res=>{
					let goods = res.data
					this.params.goodsId = goods.id
					this.params.goodsName = goods.name
					this.params.goodsPic = goods.pic
					this.params.goodsPrice = goods.price
					this.updatePrice({value:this.params.goodsNum})
				})
			},
			updatePrice(e) {
				this.params.goodsNum = e.value
				this.params.price = (this.params.goodsNum * this.params.goodsPrice).toFixed(2)
			},
			onDateConfirm(e) {
				const date = new Date(e.value)
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				const hours = String(date.getHours()).padStart(2, '0')
				const minutes = String(date.getMinutes()).padStart(2, '0')
				const seconds = String(date.getSeconds()).padStart(2, '0')
				this.params.startDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
				this.showDatePicker = false
			},
			submitOrder() {
				// 简单校验
				if (!this.params.startDate) return uni.showToast({ title: '请选择出行日期', icon: 'none' })
				if (!this.params.contactName) return uni.showToast({ title: '请填写联系人', icon: 'none' })
				if (!this.params.contactPhone) return uni.showToast({ title: '请填写联系电话', icon: 'none' })
				if (!uni.$u.test.mobile(this.params.contactPhone)) return uni.showToast({ title: '请填写正确的手机号', icon:'none' })

				uni.showLoading({
					title: '提交中...',
					mask: true
				});

				addGoodsOrder(this.params).then((orderRes) => {
					uni.hideLoading();
					uni.showModal({
						title: '提交成功',
						content: '商品订单提交成功，是否跳转到订单页面查看？',
						confirmText: '是',
						cancelText: '否',
						success: (res) => {
							if (res.confirm) {
								// 用户点击"是"，跳转到商品订单页面
								uni.navigateTo({
									url: '/pages/mine/goodsOrder/goodsOrder'
								});
							} else {
								// 用户点击"否"，返回上一页
								uni.navigateBack();
							}
						}
					});
				}).catch(() => {
					uni.hideLoading();
					uni.showToast({
						title: '订单提交失败，请重试',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding-bottom: 120rpx;
	background: #f5f5f5;
	min-height: 100vh;
}
.goods-info {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 16rpx;
	margin: 20rpx;
	padding: 20rpx;
	.goods-img {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	.goods-detail {
		flex: 1;
		.goods-name {
			font-size: 30rpx;
			color: #333;
			font-weight: bold;
			margin-bottom: 10rpx;
		}
		.goods-price {
			font-size: 32rpx;
			color: #f56c6c;
			font-weight: bold;
		}
	}
	.goods-num {
		margin-left: 20rpx;
	}
}
.form-item {
	display: flex;
	align-items: center;
	background: #fff;
	margin: 0 20rpx 20rpx 20rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	.label {
		width: 140rpx;
		color: #333;
		font-size: 28rpx;
	}
	.picker {
		flex: 1;
		display: flex;
		align-items: center;
		color: #999;
		font-size: 28rpx;
		justify-content: space-between;
	}
}
.bottom-bar {
	position: fixed;
	left: 0; right: 0; bottom: 0;
	background: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 100rpx;
	padding: 0 30rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
	z-index: 999;
	.total-price {
		font-size: 32rpx;
		color: #333;
		.price {
			color: #f56c6c;
			font-size: 36rpx;
			font-weight: bold;
			margin-left: 10rpx;
		}
	}
	.submit-btn {
		width: 110rpx;
		height: 50rpx;
		font-size: 24rpx;
	}
}
</style>
