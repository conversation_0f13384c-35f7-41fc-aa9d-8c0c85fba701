import request from '@/utils/request'

// 查询包车订单列表
export function listCarOrder(query) {
  return request({
    url: '/lease/carOrder/list',
    method: 'get',
    params: query
  })
}

// 获取付款信息
export function pay(id) {
  return request({
    url: '/lease/carOrder/pay/' + id,
    method: 'get'
  })
}

// 查询包车订单详细
export function getCarOrder(id) {
  return request({
    url: '/lease/carOrder/' + id,
    method: 'get'
  })
}

// 新增包车订单
export function addCarOrder(data) {
  return request({
    url: '/lease/carOrder',
    method: 'post',
    data: data
  })
}

// 修改包车订单
export function updateCarOrder(data) {
  return request({
    url: '/lease/carOrder',
    method: 'put',
    data: data
  })
}

// 删除包车订单
export function delCarOrder(id) {
  return request({
    url: '/lease/carOrder/' + id,
    method: 'delete'
  })
}
