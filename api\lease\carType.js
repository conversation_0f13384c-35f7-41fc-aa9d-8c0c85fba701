import request from '@/utils/request'

// 查询车辆类型列表
export function listCarType(query) {
  return request({
    url: '/lease/carType/list',
    method: 'get',
    params: query
  })
}

// 查询车辆类型详细
export function getCarType(id) {
  return request({
    url: '/lease/carType/' + id,
    method: 'get'
  })
}

// 新增车辆类型
export function addCarType(data) {
  return request({
    url: '/lease/carType',
    method: 'post',
    data: data
  })
}

// 修改车辆类型
export function updateCarType(data) {
  return request({
    url: '/lease/carType',
    method: 'put',
    data: data
  })
}

// 删除车辆类型
export function delCarType(id) {
  return request({
    url: '/lease/carType/' + id,
    method: 'delete'
  })
}
