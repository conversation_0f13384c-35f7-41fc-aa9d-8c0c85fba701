import request from '@/utils/request'

// 查询商品订单列表
export function listGoodsOrder(query) {
  return request({
    url: '/lease/goodsOrder/list',
    method: 'get',
    params: query
  })
}

// 付款接口
export function pay(id) {
  return request({
    url: '/lease/goodsOrder/pay/' + id,
    method: 'get'
  })
}

// 查询商品订单详细
export function getGoodsOrder(id) {
  return request({
    url: '/lease/goodsOrder/' + id,
    method: 'get'
  })
}

// 新增商品订单
export function addGoodsOrder(data) {
  return request({
    url: '/lease/goodsOrder',
    method: 'post',
    data: data
  })
}

// 修改商品订单
export function updateGoodsOrder(data) {
  return request({
    url: '/lease/goodsOrder',
    method: 'put',
    data: data
  })
}

// 删除商品订单
export function delGoodsOrder(id) {
  return request({
    url: '/lease/goodsOrder/' + id,
    method: 'delete'
  })
}
