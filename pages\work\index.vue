<template>
	<view class="product-container">
		<!-- 顶部图片 -->
		<image class="banner-image" src="https://score.xxybfff.com/prod-api/profile/upload/2025/04/27/swiper_20250427101439A032.jpg" mode="widthFix"></image>
		
		<!-- 分类Tabs -->
		<view class="tabs-wrapper">
			<u-tabs 
				:list="tabsList" 
				@change="onTabChange"
				itemStyle="height: 90rpx; font-size: 30rpx; padding-left: 30rpx; padding-right: 30rpx"
				keyName="name"
			></u-tabs>
		</view>
		
		<!-- 商品列表 -->
		<scroll-view 
			scroll-y 
			class="product-list"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="loadMore"
		>
			<view class="product-item" v-for="(item, index) in productList" :key="index" @click="toGoodsDetail(item.id)">
				<image class="product-image" :src="item.image" mode="aspectFill"></image>
				<view class="product-info">
					<text class="product-title">{{ item.title }}</text>
					<view class="price-book">
						<text class="product-price">¥{{ item.price }}</text>
						<u-button 
							type="primary" 
							size="mini" 
							text="立即预定"
							@click.stop="bookNow(item.id)"
              :customStyle="{
								width: '100rpx',
								height: '50rpx',
								padding: '0'
							}"
						></u-button>
					</view>
				</view>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading-more">
				<u-loadmore
					:status="loadingStatus" 
					loadingText="拼命加载中" 
					loadmoreText="上拉加载更多"
					noMoreText="没有更多数据啦"
				></u-loadmore>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import { listGoodsCategory } from "@/api/lease/goodsCategory.js"
	import { listGoods } from "@/api/lease/goods.js"
	import { baseUrl } from "../../config"
export default {
	data() {
		return {
			// Tabs数据
			tabsList: [],
			currentTab: 0,
			
			baseUrl,
			
			// 列表数据
			productList: [],
			queryParams: {
				pageNum: 1,
				pageSize: 10
			},
			loadingStatus: 'loading', // loading-加载中，nomore-没有更多，loadmore-加载更多
			refreshing: false,
			
			// 示例数据
			sampleProducts: [
				{
					id: 1,
					title: '抢! 200元代金积分',
					price: '19.00',
					image: '/static/product1.png'
				},
				{
					id: 2,
					title: '澳门市内包车埃尔法（半天）',
					price: '1400.00',
					image: '/static/product2.png'
				},
				{
					id: 3,
					title: '香港市内包车埃尔法（半天）',
					price: '1600.00',
					image: '/static/product3.png'
				}
			]
		}
	},
	
	onLoad() {
		this.getProductList();
		this.getGoodsCategoryList()
	},
	methods: {
		// Tab切换
		onTabChange(e) {
			this.currentTab = e.id;
			this.queryParams.pageNum = 1;
			this.productList = [];
			this.getProductList();
		},
		
		//获取商品分类
		getGoodsCategoryList(){
			listGoodsCategory({pageSize:10000}).then(res=>{
				this.tabsList = res.rows
				this.tabsList.unshift({name:'全部',id:0})
			})
		},
		
		// 获取商品列表
		getProductList() {
			uni.showLoading({
				title: '加载中'
			});
			
			const params = {
				...this.queryParams,
				categoryId: this.currentTab === 0 ? null : this.currentTab
			};
			
			listGoods(params).then(res => {
				const newList = res.rows.map(item => ({
					id: item.id,
					title: item.name,
					price: item.price,
					image: item.pic ? this.baseUrl + item.pic.split(',')[0] : ''
				}));
				
				if (this.queryParams.pageNum === 1) {
					this.productList = newList;
				} else {
					this.productList = [...this.productList, ...newList];
				}
				
				// 更新加载状态
				if (newList.length < this.queryParams.pageSize) {
					this.loadingStatus = 'nomore';
				} else {
					this.loadingStatus = 'loadmore';
				}
				
				uni.hideLoading();
				// 如果是下拉刷新，重置刷新状态
				if (this.refreshing) {
					this.refreshing = false;
				}
			}).catch(() => {
				uni.hideLoading();
				if (this.refreshing) {
					this.refreshing = false;
				}
				uni.showToast({
					title: '获取商品列表失败',
					icon: 'none'
				});
			});
		},
		
		// 加载更多
		loadMore() {
			if (this.loadingStatus !== 'nomore') {
				this.queryParams.pageNum++;
				this.loadingStatus = 'loading';
				this.getProductList();
			}
		},
		
		// 下拉刷新
		onRefresh() {
			this.refreshing = true;
			this.queryParams.pageNum = 1;
			this.getProductList();
		},
		
		// 立即预定
		bookNow(id) {
			uni.showToast({
				title: `预定商品 ID: ${id}`,
				icon: 'none'
			});
			// 实际项目中应该跳转到预定页面或调用预定API
		},
		
		//跳转详情页
		toGoodsDetail(id){
			uni.navigateTo({
				url:"/pages/goodsDetail/goodsDetail?id="+id
			})
		}
	}
}
</script>

<style lang="scss">
.product-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.banner-image {
	width: 100%;
}

.tabs-wrapper {
	background-color: #fff;
}

.product-list {
	flex: 1;
	padding: 20rpx;
}

.product-item {
	display: flex;
	flex-direction: row;
	background-color: #fff;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 20rpx;
}

.product-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 8rpx;
}

.product-info {
	flex: 1;
	padding-left: 20rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.product-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.price-book {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.product-price {
	font-size: 36rpx;
	color: #f00;
	font-weight: bold;
}

.loading-more {
	padding: 20rpx 0;
}
</style>