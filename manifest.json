{"name": "租车小程序", "appid": "__UNI__9D4D4B2", "description": "", "versionName": "1.1.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "wxe46a401b1e55ae43", "setting": {"urlCheck": false, "es6": false, "minified": true, "postcss": true}, "optimization": {"subPackages": true}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "获取您的出发和终点地址"}}, "requiredPrivateInfos": ["getLocation", "chooseLocation"], "plugins": {"chooseLocation": {"version": "1.0.12", "provider": "wx76a9a06e5b4e693e"}}}, "vueVersion": "2", "h5": {"template": "static/index.html", "devServer": {"port": 9090, "https": false}, "title": "RuoYi-App", "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": false}}}}