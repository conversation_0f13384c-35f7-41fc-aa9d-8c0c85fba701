<template>
	<view class="container">
		<!-- 订单状态筛选 -->
		<view class="tabs-container">
			<u-tabs
				:list="statusList"
				:current="currentStatus"
				@change="onStatusChange"
				:is-scroll="true"
				:bar-width="30"
				:bar-height="3"
				active-color="#2979ff"
				inactive-color="#666"
				:font-size="28"
			></u-tabs>
		</view>
		
		<!-- 订单列表 -->
		<view class="order-list">
			<view v-for="(item, index) in orderList" :key="index" class="order-item">
				<view class="order-header">
					<view class="order-info">
						<text class="order-no">订单编号：{{item.orderNo}}</text>
						<text class="order-time">{{item.createTime}}</text>
					</view>
					<view class="header-right">
						<view class="tags-row">
							<text class="order-type" :class="'type-' + item.serviceType">{{getServiceTypeText(item.serviceType)}}</text>
							<text class="order-status" :class="'status-' + item.status">{{getStatusText(item.status)}}</text>
						</view>
						<text class="price-value" v-if="item.price">¥{{item.price}}</text>
					</view>
				</view>
				
				<view class="order-content">
					<view class="time-info">
						<u-icon name="clock-fill" size="32" color="#2979ff"></u-icon>
						<text>{{getTimeLabel(item)}}：{{item.timePoint}}</text>
					</view>
					<view class="location-info">
						<view class="start-point">
							<u-icon name="map-fill" size="32" color="#67c23a"></u-icon>
							<text class="label">起点：</text>
							<text class="value">{{item.startPoint}}</text>
						</view>
						<view class="end-point">
							<u-icon name="map-fill" size="32" color="#f56c6c"></u-icon>
							<text class="label">终点：</text>
							<text class="value">{{item.endPoint}}</text>
						</view>
					</view>
					<view class="car-info">
						<u-icon name="car-fill" size="32" color="#e6a23c"></u-icon>
						<text>车辆类型：{{item.carType}}</text>
					</view>
				</view>
				
				<view class="order-footer">
					<view class="button-group">
						<u-button type="info" size="normal" @click="showQrCode">联系客服</u-button>
						<u-button v-if="item.status === '5'" type="success" size="normal" @click="handleComplete(item)">完成订单</u-button>
						<u-button v-if="item.status === '0'" type="error" size="normal" @click="handleCancel(item)">取消订单</u-button>
					</view>
				</view>
			</view>
			
			<!-- 无数据提示 -->
			<u-empty v-if="orderList.length === 0" mode="list" text="暂无订单"></u-empty>
			
			<!-- 加载更多 -->
			<u-loadmore v-if="orderList.length > 0" :status="loadStatus" />
		</view>
		
		<!-- 客服二维码弹窗 -->
		<u-modal
			:show="showQrCodePopup"
			title="客服微信"
			@confirm="showQrCodePopup = false"
			@close="showQrCodePopup = false"
		>
			<view class="qr-code-popup">
				<image src="/static/qrcode.png" mode="aspectFit"></image>
			</view>
		</u-modal>
	</view>
</template>

<script>
	import { listCarOrder, updateCarOrder } from "@/api/lease/carOrder.js"
	export default {
		data() {
			return {
				queryParams: {
					userId: this.$store.state.user.userId,
					pageNum: 1,
					pageSize: 10,
					status: null
				},
				orderList: [],
				currentStatus: 0,
				loadStatus: 'loading',
				showQrCodePopup: false,
				statusList: [
					{ name: '全部' },
					{ name: '待付款' },
					{ name: '已付款' },
					{ name: '待接单' },
					{ name: '司机已接单' },
					{ name: '司机已到达' },
					{ name: '进行中' },
					{ name: '已完成' },
					{ name: '已取消' }
				]
			}
		},
		onLoad() {
			this.getOrderList()
		},
		// 下拉刷新
		onPullDownRefresh() {
			this.queryParams.pageNum = 1
			this.orderList = []
			this.getOrderList()
			uni.stopPullDownRefresh()
		},
		// 上拉加载更多
		onReachBottom() {
			if (this.loadStatus === 'nomore') return
			this.queryParams.pageNum++
			this.getOrderList()
		},
		methods: {
			// 获取订单列表
			getOrderList() {
				this.loadStatus = 'loading'
				listCarOrder(this.queryParams).then(res => {
					if (this.queryParams.pageNum === 1) {
						this.orderList = res.rows
					} else {
						this.orderList = [...this.orderList, ...res.rows]
					}
					this.loadStatus = res.rows.length < this.queryParams.pageSize ? 'nomore' : 'loadmore'
				})
			},
			// 状态切换
			onStatusChange(e) {
				this.currentStatus = e.index
				// 根据标签索引映射对应的状态值
				const statusMap = {
					0: '', // 全部
					1: '0', // 待付款
					2: '1', // 已付款
					3: '2', // 待接单
					4: '3', // 司机已接单
					5: '4', // 司机已到达
					6: '5', // 进行中
					7: '6', // 已完成
					8: '9'  // 已取消
				}
				this.queryParams.status = statusMap[e.index]
				this.queryParams.pageNum = 1
				this.orderList = []
				this.getOrderList()
			},
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'0': '待付款',
					'1': '已付款',
					'2': '待接单',
					'3': '司机已接单',
					'4': '司机已到达',
					'5': '进行中',
					'6': '已完成',
					'9': '已取消'
				}
				return statusMap[status] || '未知状态'
			},
			// 获取服务类型文本
			getServiceTypeText(serviceType) {
				const typeMap = {
					'charter': '包车',
					'carpooling': '拼车',
					'airport': '接送机'
				}
				return typeMap[serviceType] || '未知类型'
			},
			// 获取时间标签
			getTimeLabel(item) {
				if (item.serviceType === 'charter') {
					return '出发时间';
				} else if (item.serviceType === 'carpooling') {
					return '出发时间';
				} else if (item.serviceType === 'airport') {
					// 接送机订单：根据airportServiceType判断
					if (item.airportServiceType === 'pickup') {
						return '到达时间'; // 接机
					} else if (item.airportServiceType === 'dropoff') {
						return '出发时间'; // 送机
					} else {
						return '时间'; // 兜底
					}
				} else {
					return '时间'; // 兜底
				}
			},
			// 显示客服二维码
			showQrCode() {
				this.showQrCodePopup = true
			},

			// 处理取消订单
			handleCancel(item) {
				uni.showModal({
					title: '提示',
					content: '确认取消该订单？',
					success: (res) => {
						if (res.confirm) {
							updateCarOrder({
								id: item.id,
								status: 9
							}).then(() => {
								uni.showToast({
									title: '订单已取消',
									icon: 'success'
								})
								this.getOrderList()
							})
						}
					}
				})
			},
			// 处理完成订单
			handleComplete(item) {
				uni.showModal({
					title: '提示',
					content: '确认完成该订单？',
					success: (res) => {
						if (res.confirm) {
							updateCarOrder({
								id: item.id,
								status: 6
							}).then(() => {
								uni.showToast({
									title: '订单已完成',
									icon: 'success'
								})
								this.getOrderList()
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.tabs-container {
	background-color: #fff;
	border-radius: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: sticky;
	top: 0;
	z-index: 100;
}

.order-list {
	margin-top: 20rpx;
}

.order-item {
	background-color: #fff;
	border-radius: 10rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
	}
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #eee;
	
	.order-info {
		display: flex;
		flex-direction: column;
		gap: 10rpx;
		flex: 1;
	}

	.order-no {
		font-size: 28rpx;
		color: #666;
	}

	.order-type {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		border-radius: 16rpx;
		font-weight: 500;
		white-space: nowrap;
		line-height: 1;

		&.type-charter {
			color: #1890ff;
			background-color: #e6f7ff;
			border: 1px solid #91d5ff;
		}

		&.type-carpooling {
			color: #52c41a;
			background-color: #f6ffed;
			border: 1px solid #b7eb8f;
		}

		&.type-airport {
			color: #fa8c16;
			background-color: #fff7e6;
			border: 1px solid #ffd591;
		}
	}

	.order-time {
		font-size: 24rpx;
		color: #999;
	}
	
	.header-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 10rpx;
	}

	.tags-row {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.price-value {
		font-size: 32rpx;
		color: #f56c6c;
		font-weight: bold;
	}
	
	.order-status {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		border-radius: 16rpx;
		font-weight: 500;
		white-space: nowrap;
		line-height: 1;

		&.status-0 {
			color: #ff4d4f;
			background-color: #fff2f0;
			border: 1px solid #ffccc7;
		}

		&.status-1 {
			color: #1890ff;
			background-color: #e6f7ff;
			border: 1px solid #91d5ff;
		}

		&.status-2 {
			color: #fa8c16;
			background-color: #fff7e6;
			border: 1px solid #ffd591;
		}

		&.status-3 {
			color: #52c41a;
			background-color: #f6ffed;
			border: 1px solid #b7eb8f;
		}

		&.status-4 {
			color: #52c41a;
			background-color: #f6ffed;
			border: 1px solid #b7eb8f;
		}

		&.status-5 {
			color: #1890ff;
			background-color: #e6f7ff;
			border: 1px solid #91d5ff;
		}

		&.status-6 {
			color: #52c41a;
			background-color: #f6ffed;
			border: 1px solid #b7eb8f;
		}

		&.status-9 {
			color: #8c8c8c;
			background-color: #f5f5f5;
			border: 1px solid #d9d9d9;
		}
	}
}

.order-content {
	.time-info, .car-info {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 20rpx;
		
		text {
			margin-left: 10rpx;
		}
	}
	
	.location-info {
		.start-point, .end-point {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			
			.label {
				font-size: 28rpx;
				color: #666;
				width: 80rpx;
				margin-left: 10rpx;
			}
			
			.value {
				font-size: 28rpx;
				color: #333;
				flex: 1;
			}
		}
	}
}

.order-footer {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-top: 30rpx;
	padding-top: 20rpx;
	border-top: 1px solid #eee;
	
	.button-group {
		display: flex;
		gap: 20rpx;
		
		:deep(.u-button) {
			height: 70rpx;
			padding: 0 30rpx;
			font-size: 28rpx;
		}
	}
}

.qr-code-popup {
	padding: 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	
	image {
		width: 400rpx;
		height: 400rpx;
	}
}
</style>
